import { useState, useMemo } from 'react'
import { use<PERSON><PERSON>ms, Link, useNavigate } from 'react-router-dom'
import { PageHeader } from '../shared'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Progress } from '../ui/progress'
import { Textarea } from '../ui/textarea'
import { Checkbox } from '../ui/checkbox'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { cn } from '../../lib/utils'
import { useAreaStore } from '../../store/areaStore'
import { useProjectStore } from '../../store/projectStore'
import { useLanguage } from '../../contexts/LanguageContext'
import CreateAreaDialog from './CreateAreaDialog'
import ProjectCard from './ProjectCard'
import HabitItem from './HabitItem'
import CreateHabitDialog from './CreateHabitDialog'
import type { Area } from '../../../../shared/types'

export function AreaDetailPage() {
  const { areaId } = useParams<{ areaId: string }>()
  const navigate = useNavigate()
  const { areas, updateArea, deleteArea, archiveArea } = useAreaStore()
  const { projects } = useProjectStore()
  const { t } = useLanguage()

  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isCreateHabitDialogOpen, setIsCreateHabitDialogOpen] = useState(false)
  const [editingHabit, setEditingHabit] = useState<any>(null)
  const [habits, setHabits] = useState<any[]>([])
  const [notes, setNotes] = useState('')
  const [isNotesEditing, setIsNotesEditing] = useState(false)
  const [checklist, setChecklist] = useState<
    Array<{ id: string; text: string; completed: boolean }>
  >([
    {
      id: '1',
      text: t('pages.areas.detail.reviewStandardsWeekly') || 'Review area standards weekly',
      completed: false
    },
    {
      id: '2',
      text: t('pages.areas.detail.updateHabitsTracking') || 'Update habits tracking',
      completed: true
    },
    {
      id: '3',
      text: t('pages.areas.detail.assessProgress') || 'Assess progress against goals',
      completed: false
    }
  ])

  const area = areas.find((a) => a.id === areaId)
  const relatedProjects = projects.filter(
    (project) => project.areaId === areaId && !project.archived
  )

  if (!area) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-12">
          <div className="text-4xl mb-4">🏠</div>
          <h2 className="text-xl font-semibold mb-2">{t('pages.areas.detail.notFound')}</h2>
          <p className="text-muted-foreground mb-4">
            {t('pages.areas.detail.notFoundDescription')}
          </p>
          <Button asChild>
            <Link to="/areas">{t('pages.areas.detail.backToAreasButton')}</Link>
          </Button>
        </div>
      </div>
    )
  }

  // Calculate area statistics
  const stats = useMemo(() => {
    const totalProjects = relatedProjects.length
    const completedProjects = relatedProjects.filter((p) => p.status === 'Completed').length
    const activeHabits = (area as any).habits?.length || 0
    const completedChecklist = checklist.filter((item) => item.completed).length
    const checklistProgress =
      checklist.length > 0 ? Math.round((completedChecklist / checklist.length) * 100) : 0

    return {
      totalProjects,
      completedProjects,
      activeHabits,
      checklistProgress,
      completedChecklist,
      totalChecklist: checklist.length
    }
  }, [relatedProjects, (area as any).habits, checklist])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'Needs Attention':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'On Hold':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'Review Required':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const handleEditArea = async (areaData: Omit<Area, 'id' | 'createdAt' | 'updatedAt'>) => {
    updateArea(area.id, {
      ...areaData,
      updatedAt: new Date()
    })
    setIsEditDialogOpen(false)
  }

  const handleDeleteArea = () => {
    if (confirm(t('pages.areas.detail.confirmDeleteArea', { name: area.name }))) {
      deleteArea(area.id)
      navigate('/areas')
    }
  }

  const handleArchiveArea = () => {
    if (confirm(t('pages.areas.detail.confirmArchiveArea', { name: area.name }))) {
      archiveArea(area.id)
      navigate('/areas')
    }
  }

  const handleSaveNotes = () => {
    // In a real implementation, this would save to a notes field or separate notes system
    setIsNotesEditing(false)
  }

  const handleCreateHabit = async (habitData: any) => {
    const newHabit = {
      ...habitData,
      id: `habit-${Date.now()}`,
      areaId: area.id,
      records: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    setHabits((prev) => [...prev, newHabit])
  }

  const handleEditHabit = async (habitData: any) => {
    if (editingHabit) {
      setHabits((prev) =>
        prev.map((habit) =>
          habit.id === editingHabit.id
            ? { ...habit, ...habitData, updatedAt: new Date().toISOString() }
            : habit
        )
      )
      setEditingHabit(null)
    }
  }

  const handleDeleteHabit = (habitId: string) => {
    if (confirm(t('pages.areas.detail.confirmDeleteHabit'))) {
      setHabits((prev) => prev.filter((habit) => habit.id !== habitId))
    }
  }

  const handleToggleHabit = (habitId: string, date: string, completed: boolean) => {
    setHabits((prev) =>
      prev.map((habit) => {
        if (habit.id === habitId) {
          const existingRecordIndex = habit.records.findIndex((r: any) => r.date === date)
          const updatedRecords = [...habit.records]

          if (existingRecordIndex >= 0) {
            updatedRecords[existingRecordIndex] = {
              ...updatedRecords[existingRecordIndex],
              completed
            }
          } else {
            updatedRecords.push({ date, completed, value: undefined, note: undefined })
          }

          return { ...habit, records: updatedRecords, updatedAt: new Date().toISOString() }
        }
        return habit
      })
    )
  }

  const handleChecklistToggle = (itemId: string) => {
    setChecklist((prev) =>
      prev.map((item) => (item.id === itemId ? { ...item, completed: !item.completed } : item))
    )
  }

  const addChecklistItem = () => {
    const newItem = {
      id: Date.now().toString(),
      text: t('pages.areas.detail.newChecklistItem'),
      completed: false
    }
    setChecklist((prev) => [...prev, newItem])
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <Button variant="ghost" size="sm" asChild>
              <Link to="/areas" className="text-muted-foreground hover:text-foreground">
                {t('pages.areas.detail.backToAreas')}
              </Link>
            </Button>
          </div>

          <PageHeader
            title={area.name}
            description={area.description || undefined}
            badge={{ text: t('nav.areas'), variant: 'secondary' }}
            className="border-l-4 border-area pl-6"
          />
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 5v.01M12 12v.01M12 19v.01"
                />
              </svg>
              {t('pages.areas.detail.actions')}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => setIsEditDialogOpen(true)}>
              {t('pages.areas.detail.editArea')}
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleArchiveArea}>
              {t('pages.areas.detail.archiveArea')}
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={handleDeleteArea}
              className="text-red-600 focus:text-red-600"
            >
              {t('pages.areas.detail.deleteArea')}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Area Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Info */}
        <div className="lg:col-span-2 space-y-6">
          {/* Status and Progress */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>{t('pages.areas.detail.areaStatus')}</span>
                <Badge
                  variant="outline"
                  className={cn('text-sm', getStatusColor(area.status || 'Active'))}
                >
                  {t(
                    `pages.areas.filters.status.${(area.status || 'Active').toLowerCase().replace(' ', '')}`
                  ) ||
                    area.status ||
                    'Active'}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>{t('pages.areas.detail.standardsChecklist')}</span>
                  <span className="font-medium">{stats.checklistProgress}%</span>
                </div>
                <Progress value={stats.checklistProgress} className="h-3" />
              </div>

              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-center">
                <div className="space-y-1">
                  <div className="text-2xl font-bold text-blue-600">{stats.totalProjects}</div>
                  <div className="text-xs text-muted-foreground">
                    {t('pages.areas.detail.relatedProjects')}
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-2xl font-bold text-green-600">{stats.completedProjects}</div>
                  <div className="text-xs text-muted-foreground">
                    {t('pages.areas.detail.completed')}
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-2xl font-bold text-purple-600">{stats.activeHabits}</div>
                  <div className="text-xs text-muted-foreground">
                    {t('pages.areas.detail.activeHabits')}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Standard */}
          {area.standard && (
            <Card>
              <CardHeader>
                <CardTitle>{t('pages.areas.detail.areaStandard')}</CardTitle>
                <CardDescription>{t('pages.areas.detail.areaStandardDescription')}</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">{area.standard}</p>
              </CardContent>
            </Card>
          )}

          {/* Standards Checklist */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>{t('pages.areas.detail.standardsChecklist')}</CardTitle>
                  <CardDescription>
                    {t('pages.areas.detail.standardsChecklistDescription')}
                  </CardDescription>
                </div>
                <Button variant="outline" size="sm" onClick={addChecklistItem}>
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    />
                  </svg>
                  {t('pages.areas.detail.addItem')}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {checklist.map((item) => (
                  <div
                    key={item.id}
                    className="flex items-center gap-3 p-2 rounded-lg hover:bg-accent/50"
                  >
                    <Checkbox
                      checked={item.completed}
                      onCheckedChange={() => handleChecklistToggle(item.id)}
                    />
                    <span
                      className={cn(
                        'flex-1 text-sm',
                        item.completed && 'line-through text-muted-foreground'
                      )}
                    >
                      {item.text}
                    </span>
                  </div>
                ))}
                {checklist.length === 0 && (
                  <div className="text-center py-4 text-muted-foreground">
                    <p className="text-sm">{t('pages.areas.detail.noChecklistItems')}</p>
                    <p className="text-xs mt-1">{t('pages.areas.detail.addChecklistHint')}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Notes */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>{t('pages.areas.detail.areaNotes')}</CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsNotesEditing(!isNotesEditing)}
                >
                  {isNotesEditing ? t('pages.areas.detail.cancel') : t('pages.areas.detail.edit')}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {isNotesEditing ? (
                <div className="space-y-3">
                  <Textarea
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    placeholder={t('pages.areas.detail.notesPlaceholder')}
                    rows={6}
                  />
                  <div className="flex gap-2">
                    <Button size="sm" onClick={handleSaveNotes}>
                      {t('pages.areas.detail.saveNotes')}
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => setIsNotesEditing(false)}>
                      {t('pages.areas.detail.cancel')}
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-sm text-muted-foreground">
                  {notes || t('pages.areas.detail.noNotesYet')}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Habits */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>{t('pages.areas.detail.areaHabits')}</CardTitle>
                  <CardDescription>{t('pages.areas.detail.areaHabitsDescription')}</CardDescription>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsCreateHabitDialogOpen(true)}
                >
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    />
                  </svg>
                  {t('pages.areas.detail.addHabit')}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {habits.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <div className="text-4xl mb-2">🎯</div>
                  <p className="text-sm">{t('pages.areas.detail.noHabitsYet')}</p>
                  <p className="text-xs mt-1">{t('pages.areas.detail.addFirstHabit')}</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {habits.map((habit) => (
                    <HabitItem
                      key={habit.id}
                      habit={habit}
                      onEdit={setEditingHabit}
                      onDelete={handleDeleteHabit}
                      onToggle={handleToggleHabit}
                    />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Key Information */}
          <Card>
            <CardHeader>
              <CardTitle>{t('pages.areas.detail.keyInformation')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">
                  {t('pages.areas.detail.reviewFrequency')}
                </span>
                <span>
                  {area.reviewFrequency
                    ? t(
                        `pages.areas.dialog.reviewFrequencyOptions.${area.reviewFrequency.toLowerCase()}`
                      ) || area.reviewFrequency
                    : t('pages.areas.detail.weekly')}
                </span>
              </div>

              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">{t('pages.areas.detail.created')}</span>
                <span>{new Date(area.createdAt).toLocaleDateString()}</span>
              </div>

              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">{t('pages.areas.detail.lastUpdated')}</span>
                <span>{new Date(area.updatedAt).toLocaleDateString()}</span>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>{t('pages.areas.detail.quickActions')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button
                variant="outline"
                size="sm"
                className="w-full justify-start"
                onClick={() => setIsCreateHabitDialogOpen(true)}
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
                {t('pages.areas.detail.addHabit')}
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                  />
                </svg>
                {t('pages.areas.detail.linkProject')}
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  />
                </svg>
                {t('pages.areas.detail.viewAnalytics')}
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Related Projects */}
      {relatedProjects.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>{t('pages.areas.detail.relatedProjects')}</CardTitle>
            <CardDescription>{t('pages.areas.detail.relatedProjectsDescription')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {relatedProjects.map((project) => (
                <ProjectCard key={project.id} project={project} className="max-w-none" />
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Edit Area Dialog */}
      <CreateAreaDialog
        isOpen={isEditDialogOpen}
        onClose={() => setIsEditDialogOpen(false)}
        onSubmit={handleEditArea}
        initialData={area}
      />

      {/* Create/Edit Habit Dialog */}
      <CreateHabitDialog
        isOpen={isCreateHabitDialogOpen || !!editingHabit}
        onClose={() => {
          setIsCreateHabitDialogOpen(false)
          setEditingHabit(null)
        }}
        onSubmit={editingHabit ? handleEditHabit : handleCreateHabit}
        initialData={editingHabit || undefined}
        areaId={area.id}
      />
    </div>
  )
}

export default AreaDetailPage
