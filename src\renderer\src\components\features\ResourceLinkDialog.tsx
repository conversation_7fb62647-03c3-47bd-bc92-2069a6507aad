import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Badge } from '../ui/badge'
import { Checkbox } from '../ui/checkbox'
import { databaseApi, fileSystemApi } from '../../lib/api'
import { useUIStore } from '../../store/uiStore'
import { useUserSettingsStore } from '../../store/userSettingsStore'
import { getResourcesPath } from '../../plugins/wikilink/utils'
import { cn } from '../../lib/utils'
import type { ResourceLink } from '../../../../shared/types'

interface ResourceLinkDialogProps {
  isOpen: boolean
  onClose: () => void
  projectId: string
  onResourcesLinked: (resources: ResourceLink[]) => void
}

export function ResourceLinkDialog({
  isOpen,
  onClose,
  projectId,
  onResourcesLinked
}: ResourceLinkDialogProps) {
  const [availableResources, setAvailableResources] = useState<ResourceLink[]>([])
  const [selectedResourceIds, setSelectedResourceIds] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [loading, setLoading] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [showFileSystemResources, setShowFileSystemResources] = useState(false)
  const [fileSystemResources, setFileSystemResources] = useState<Array<{path: string, name: string}>>([])
  const { addNotification } = useUIStore()
  const { settings } = useUserSettingsStore()

  // Load available resources when dialog opens
  useEffect(() => {
    if (isOpen) {
      loadAvailableResources()
    }
  }, [isOpen, projectId])

  const loadAvailableResources = async () => {
    try {
      setLoading(true)

      // Get already linked resources for this project
      const linkedResourcesResult = await databaseApi.getProjectResources(projectId)
      if (!linkedResourcesResult.success) {
        throw new Error(linkedResourcesResult.error || 'Failed to load linked resources')
      }

      const linkedResourcePaths = new Set((linkedResourcesResult.data || []).map(r => r.resourcePath))

      // Scan file system for all markdown files
      await scanFileSystemResources(linkedResourcePaths)
    } catch (error) {
      console.error('Failed to load available resources:', error)
      addNotification({
        type: 'error',
        title: 'Failed to load resources',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    } finally {
      setLoading(false)
    }
  }

  const scanFileSystemResources = async (linkedPaths: Set<string>) => {
    try {
      const resourcesPath = await getResourcesPath(settings)


      // Recursively scan for markdown files
      const markdownFiles: Array<{path: string, name: string}> = []

      const scanDirectory = async (dirPath: string, relativePath = '') => {
        try {
          const result = await fileSystemApi.listDirectory(dirPath)
          if (!result.success || !result.data) return

          for (const item of result.data) {
            if (item.isFile && item.name.endsWith('.md')) {
              // Skip already linked files
              if (linkedPaths.has(item.path)) continue

              const displayName = relativePath ? `${relativePath}/${item.name}` : item.name
              markdownFiles.push({
                path: item.path, // 使用完整的绝对路径
                name: displayName // 使用相对路径作为显示名称
              })

            } else if (item.isDirectory) {
              const subRelativePath = relativePath ? `${relativePath}/${item.name}` : item.name
              await scanDirectory(item.path, subRelativePath)
            }
          }
        } catch (error) {
          console.error(`Failed to scan directory ${dirPath}:`, error)
        }
      }

      await scanDirectory(resourcesPath)

      setFileSystemResources(markdownFiles)
      setAvailableResources([]) // Clear database resources since we're going direct
    } catch (error) {
      console.error('Failed to scan file system resources:', error)
    }
  }

  const handleSelectFileSystemResource = (file: {path: string, name: string}) => {
    // Add to selected resources (using path as ID for file system resources)
    const fileId = `fs-${file.path}`
    setSelectedResourceIds(prev =>
      prev.includes(fileId)
        ? prev.filter(id => id !== fileId)
        : [...prev, fileId]
    )
  }

  const filteredFileSystemResources = fileSystemResources.filter(file => {
    if (!searchQuery) return true

    const query = searchQuery.toLowerCase()
    return (
      file.name.toLowerCase().includes(query) ||
      file.path.toLowerCase().includes(query)
    )
  })

  const handleResourceToggle = (resourceId: string) => {
    setSelectedResourceIds(prev => 
      prev.includes(resourceId)
        ? prev.filter(id => id !== resourceId)
        : [...prev, resourceId]
    )
  }

  const handleSelectAll = () => {
    const allFileIds = filteredFileSystemResources.map(f => `fs-${f.path}`)
    if (selectedResourceIds.length === allFileIds.length) {
      setSelectedResourceIds([])
    } else {
      setSelectedResourceIds(allFileIds)
    }
  }

  const handleSubmit = async () => {
    if (selectedResourceIds.length === 0) {
      addNotification({
        type: 'warning',
        title: 'No resources selected',
        message: 'Please select at least one resource to link'
      })
      return
    }

    try {
      setSubmitting(true)
      const linkedResources: ResourceLink[] = []

      // Create ResourceLink records for selected files
      for (const resourceId of selectedResourceIds) {
        if (resourceId.startsWith('fs-')) {
          // File system resource
          const filePath = resourceId.substring(3) // Remove 'fs-' prefix
          const file = fileSystemResources.find(f => f.path === filePath)
          if (!file) continue



          const result = await databaseApi.createResource({
            resourcePath: file.path, // 确保使用完整路径
            title: file.name.replace('.md', ''),
            projectId: projectId, // Directly link to project
            areaId: undefined
          })


          if (result.success) {
            linkedResources.push(result.data)
          } else {
            throw new Error(`Failed to link resource: ${result.error}`)
          }
        }
      }

      addNotification({
        type: 'success',
        title: 'Resources linked successfully',
        message: `${linkedResources.length} resource${linkedResources.length > 1 ? 's' : ''} linked to project`
      })

      onResourcesLinked(linkedResources)
      onClose()

      // Reset state
      setSelectedResourceIds([])
      setSearchQuery('')
    } catch (error) {
      console.error('Failed to link resources:', error)
      addNotification({
        type: 'error',
        title: 'Failed to link resources',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    } finally {
      setSubmitting(false)
    }
  }

  const handleClose = () => {
    if (!submitting) {
      setSelectedResourceIds([])
      setSearchQuery('')
      onClose()
    }
  }

  const getResourceDisplayName = (resource: ResourceLink) => {
    if (resource.title) return resource.title
    
    // Extract filename from path
    const pathParts = resource.resourcePath.split(/[/\\]/)
    const filename = pathParts[pathParts.length - 1]
    return filename.replace('.md', '')
  }

  const getResourcePath = (resource: ResourceLink) => {
    // Show relative path for better readability
    const pathParts = resource.resourcePath.split(/[/\\]/)
    if (pathParts.length > 3) {
      return `.../${pathParts.slice(-3).join('/')}`
    }
    return resource.resourcePath
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>Link Resources to Project</DialogTitle>
          <DialogDescription>
            Select resources from your knowledge base to link to this project.
            Linked resources will appear in the project details and create bidirectional connections.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search */}
          <div className="space-y-2">
            <Input
              placeholder="Search resources by title or path..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full"
            />
          </div>

          {/* Select All */}
          {filteredFileSystemResources.length > 0 && (
            <div className="flex items-center space-x-2">
              <Checkbox
                id="select-all"
                checked={selectedResourceIds.length === filteredFileSystemResources.length}
                onCheckedChange={handleSelectAll}
              />
              <label htmlFor="select-all" className="text-sm font-medium">
                Select all ({filteredFileSystemResources.length} files)
              </label>
            </div>
          )}

          {/* Resource List */}
          <div className="h-[300px] border rounded-md overflow-y-auto">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : filteredFileSystemResources.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <div className="text-4xl mb-2">📄</div>
                <p className="text-sm">
                  {searchQuery ? 'No files match your search' : 'No Markdown files found'}
                </p>
                <p className="text-xs mt-1">
                  {searchQuery ? 'Try a different search term' : 'Create some .md files in your knowledge base first'}
                </p>
              </div>
            ) : (
              <div className="p-4 space-y-3">
                {filteredFileSystemResources.map((file, index) => {
                  const fileId = `fs-${file.path}`
                  return (
                    <div
                      key={fileId}
                      className={cn(
                        'flex items-start space-x-3 p-3 rounded-lg border cursor-pointer hover:bg-accent/50 transition-colors',
                        selectedResourceIds.includes(fileId) && 'bg-accent border-primary'
                      )}
                      onClick={() => handleSelectFileSystemResource(file)}
                    >
                      <Checkbox
                        checked={selectedResourceIds.includes(fileId)}
                        onCheckedChange={() => handleSelectFileSystemResource(file)}
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium text-sm truncate">
                            {file.name.replace('.md', '')}
                          </h4>
                          <Badge variant="outline" className="text-xs">
                            .md
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground truncate">
                          {file.path}
                        </p>
                      </div>
                    </div>
                  )
                })}
              </div>
            )}
          </div>

          {/* Selection Summary */}
          {selectedResourceIds.length > 0 && (
            <div className="p-3 bg-primary/10 rounded-lg border border-primary/20">
              <div className="text-sm font-medium text-primary">
                {selectedResourceIds.length} resource{selectedResourceIds.length > 1 ? 's' : ''} selected
              </div>
              <div className="text-xs text-primary/80 mt-1">
                These resources will be linked to your project and appear in the project details.
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={submitting}>
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit} 
            disabled={submitting || selectedResourceIds.length === 0}
          >
            {submitting ? 'Linking...' : `Link ${selectedResourceIds.length} Resource${selectedResourceIds.length > 1 ? 's' : ''}`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default ResourceLinkDialog
