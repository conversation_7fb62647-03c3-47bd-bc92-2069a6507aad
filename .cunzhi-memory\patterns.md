# 常用模式和最佳实践

- MarkdownEditor 组件实现了真正的单实例模式，解决了内容更新重复问题。关键技术：1) 移除 key 属性避免组件重建；2) 使用 isUpdatingRef 标记防止 replaceAll 和 markdownUpdated 循环调用；3) 优化 useEffect 依赖避免重复初始化。markdownUpdated 用于用户编辑，replaceAll 用于程序化更新，通过标记机制避免循环。
- 已完成 WikiLink 双向链接插件开发。插件支持 [[页面名称]] 和 [[页面名称|显示文本]] 语法，包含自动补全、悬停预览、双向链接功能。关键技术：基于 Milkdown 插件系统，使用 ProseMirror 节点定义，集成文件系统 API。修复了 markdownUpdated 循环更新问题，使用 isUpdatingRef 标记防止程序化更新触发回调循环。
- WikiLink 插件集成方案：采用最小化集成方式，在 MarkdownEditor.tsx 的 useEditor 钩子中通过 crepe.editor.use() 注入插件，只启用输入规则功能，配置页面点击和链接创建回调，避免与 Crepe 内置功能冲突
- WikiLink 渲染持久性问题根因：Remark 插件被禁用导致 Markdown 序列化/反序列化链路断裂。输入规则只负责输入转换，但缺少 Markdown 解析阶段的 [[内容]] 文本到 WikiLink 节点的转换。解决方案：启用 wikiLinkRemarkPlugin 提供完整的 Markdown 解析支持。
- WikiLink 嵌套编辑器架构实现：1) NestedPreviewManager 单例管理多层级预览窗口 2) PreviewInstance 类管理独立的 Milkdown 编辑器实例 3) 支持最大5层嵌套，每层有独立的z-index和位置偏移 4) 完整的生命周期管理：创建时异步加载内容和编辑器，销毁时清理所有资源 5) createNestedPreviewPlugin 为每个预览编辑器注入嵌套预览功能 6) 智能定位避免超出视窗边界，层级颜色标识便于调试
- 实现了右侧滑出式任务详情面板：TaskDetailPanel 组件支持完整的任务编辑功能，包括基本信息、状态管理、时间管理、进度跟踪、项目关联等。使用 CSS Transform 实现平滑滑动动画，支持点击外部区域和 ESC 键关闭。集成到 ProjectDetailPage 中，点击任务卡片即可打开详情面板进行编辑。
- PaoLife 任务管理功能优化完成：1) 实现无限层级子任务系统，支持任意深度嵌套，智能展开折叠，层级连接线显示，性能优化的虚拟化渲染；2) 实现右侧滑出式任务详情面板，完整编辑功能，平滑动画，响应式设计；3) 性能优化包括 memo 组件、虚拟化列表、响应式缩进；4) 提供测试数据生成器验证大量嵌套任务性能。
