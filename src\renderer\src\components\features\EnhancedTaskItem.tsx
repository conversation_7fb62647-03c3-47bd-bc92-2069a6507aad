import { useState } from 'react'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Progress } from '../ui/progress'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import {
  Clock,
  Calendar,
  MoreHorizontal,
  Settings,
  Play,
  Pause,
  CheckCircle,
  AlertCircle,
  Timer
} from 'lucide-react'
import { cn } from '../../lib/utils'
import type { Task } from '../../../../shared/types'

interface EnhancedTaskItemProps {
  task: Task
  level?: number // 层级深度，用于缩进显示
  onToggleComplete: (task: Task) => void
  onUpdateStatus: (task: Task, status: string) => void
  onOpenAttributes: (task: Task) => void
  onDelete?: (task: Task) => void
  showHierarchy?: boolean // 是否显示层级线条
}

export function EnhancedTaskItem({
  task,
  level = 0,
  onToggleComplete,
  onUpdateStatus,
  onOpenAttributes,
  onDelete,
  showHierarchy = true
}: EnhancedTaskItemProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-gray-100 text-gray-600 border-gray-200'
    }
  }

  const shouldShowPriority = (priority?: string) => {
    return priority && priority !== 'none' && priority !== ''
  }

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'in_progress':
        return <Play className="h-3 w-3" />
      case 'blocked':
        return <AlertCircle className="h-3 w-3" />
      case 'review':
        return <Clock className="h-3 w-3" />
      case 'done':
        return <CheckCircle className="h-3 w-3" />
      default:
        return <Timer className="h-3 w-3" />
    }
  }

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'blocked':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'review':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'done':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-gray-100 text-gray-600 border-gray-200'
    }
  }

  const formatDate = (date: string | Date | null) => {
    if (!date) return null
    return new Date(date).toLocaleDateString()
  }

  const formatHours = (hours?: number) => {
    if (!hours) return null
    return `${hours}h`
  }

  const isOverdue = task.deadline && new Date(task.deadline) < new Date() && !task.completed

  const quickStatusActions = [
    { status: 'todo', label: 'To Do', icon: Timer },
    { status: 'in_progress', label: 'In Progress', icon: Play },
    { status: 'review', label: 'Review', icon: Clock },
    { status: 'done', label: 'Done', icon: CheckCircle }
  ]

  return (
    <div
      className={cn(
        'p-4 border rounded-lg hover:bg-accent/50 transition-colors relative',
        task.completed && 'opacity-60',
        isOverdue && 'border-red-200 bg-red-50',
        showHierarchy && level > 0 && 'ml-6' // 基础缩进
      )}
    >
      {/* 层级指示器 - 简化版本 */}
      {showHierarchy && level > 0 && (
        <div className="absolute -left-6 top-0 bottom-0 w-6 flex items-center justify-center">
          <div className="w-px h-full bg-gray-200" />
          <div className="absolute top-6 left-1/2 w-3 h-px bg-gray-200 -translate-x-1/2" />
        </div>
      )}

      <div className="flex items-start gap-3 w-full">
        {/* Checkbox */}
        <input
          type="checkbox"
          checked={task.completed}
          onChange={() => onToggleComplete(task)}
          className="mt-1"
          aria-label={`Mark task "${task.content}" as ${task.completed ? 'incomplete' : 'complete'}`}
        />

        {/* Main Content */}
        <div className="flex-1 min-w-0">
          {/* Task Title and Basic Info */}
          <div className="flex items-center gap-2 mb-2">
            <h4 className={cn(
              "font-medium text-sm",
              task.completed && "line-through text-muted-foreground"
            )}>
              {task.content}
            </h4>

            {/* Priority Badge */}
            {shouldShowPriority(task.priority) && (
              <Badge variant="outline" className={cn("text-xs", getPriorityColor(task.priority))}>
                {task.priority}
              </Badge>
            )}

            {/* Status Badge */}
            {task.status && task.status !== 'todo' && (
              <Badge variant="outline" className={cn("text-xs", getStatusColor(task.status))}>
                {getStatusIcon(task.status)}
                <span className="ml-1 capitalize">{task.status.replace('_', ' ')}</span>
              </Badge>
            )}

            {/* Overdue Warning */}
            {isOverdue && (
              <Badge variant="destructive" className="text-xs">
                Overdue
              </Badge>
            )}
          </div>

          {/* Progress Bar */}
          {task.progress !== undefined && task.progress > 0 && (
            <div className="mb-2">
              <div className="flex items-center gap-2 mb-1">
                <span className="text-xs text-muted-foreground">Progress</span>
                <span className="text-xs font-medium">{task.progress}%</span>
              </div>
              <Progress value={task.progress} className="h-2" />
            </div>
          )}

          {/* Time and Date Info */}
          <div className="flex items-center gap-4 text-xs text-muted-foreground">
            {task.deadline && (
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span>Due: {formatDate(task.deadline)}</span>
              </div>
            )}

            {task.estimatedHours && (
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>Est: {formatHours(task.estimatedHours)}</span>
              </div>
            )}

            {task.actualHours && (
              <div className="flex items-center gap-1">
                <Timer className="h-3 w-3" />
                <span>Actual: {formatHours(task.actualHours)}</span>
              </div>
            )}
          </div>

          {/* Description */}
          {task.description && (
            <p className="text-xs text-muted-foreground mt-2 line-clamp-2">
              {task.description}
            </p>
          )}

          {/* Blocked Reason */}
          {task.status === 'blocked' && task.blockedBy && (
            <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs">
              <span className="font-medium text-red-800">Blocked: </span>
              <span className="text-red-700">{task.blockedBy}</span>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex items-center gap-1">
          {/* Quick Status Actions */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                {getStatusIcon(task.status)}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {quickStatusActions.map((action) => (
                <DropdownMenuItem
                  key={action.status}
                  onClick={() => onUpdateStatus(task, action.status)}
                  className="flex items-center gap-2"
                >
                  <action.icon className="h-4 w-4" />
                  {action.label}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* More Actions */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onOpenAttributes(task)}>
                <Settings className="h-4 w-4 mr-2" />
                Task Attributes
              </DropdownMenuItem>
              {onDelete && (
                <DropdownMenuItem 
                  onClick={() => onDelete(task)}
                  className="text-destructive"
                >
                  Delete
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  )
}

export default EnhancedTaskItem
